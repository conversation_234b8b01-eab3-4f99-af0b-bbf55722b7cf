export interface ApiResponse {
    status: number;
    body: any;
    ok: boolean;
    error?: string;
}
export interface ApiError {
    ok: false;
    status: number;
    error: string;
}
/**
 * Make HTTP request with error handling
 * Never exposes secrets in error messages
 */
export declare function makeRequest(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, data?: any): Promise<ApiResponse>;
//# sourceMappingURL=http.d.ts.map