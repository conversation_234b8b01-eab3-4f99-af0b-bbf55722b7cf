"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeRequest = makeRequest;
const axios_1 = __importStar(require("axios"));
/**
 * Make HTTP request with error handling
 * Never exposes secrets in error messages
 */
async function makeRequest(method, url, data) {
    try {
        const response = await (0, axios_1.default)({
            method,
            url,
            data,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'roze-mcp-bridge/1.0.0',
            },
            timeout: 30000, // 30 second timeout
            validateStatus: () => true, // Don't throw on HTTP error status
        });
        return {
            status: response.status,
            body: response.data,
            ok: response.status >= 200 && response.status < 300,
        };
    }
    catch (error) {
        // Handle network/timeout errors
        if (error instanceof axios_1.AxiosError) {
            return {
                status: error.response?.status || 0,
                body: null,
                ok: false,
                error: sanitizeError(error.message),
            };
        }
        return {
            status: 0,
            body: null,
            ok: false,
            error: 'Unknown network error',
        };
    }
}
/**
 * Sanitize error messages to prevent secret leakage
 */
function sanitizeError(message) {
    // Remove potential secrets from error messages
    return message
        .replace(/api[_-]?key[s]?[=:]\s*[^\s&]+/gi, 'api_key=***')
        .replace(/token[s]?[=:]\s*[^\s&]+/gi, 'token=***')
        .replace(/secret[s]?[=:]\s*[^\s&]+/gi, 'secret=***')
        .replace(/password[s]?[=:]\s*[^\s&]+/gi, 'password=***');
}
//# sourceMappingURL=http.js.map