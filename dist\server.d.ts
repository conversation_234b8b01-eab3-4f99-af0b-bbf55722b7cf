#!/usr/bin/env node
/**
 * Roze MCP Bridge Server
 *
 * Exposes shared tools for VS Code windows to use the same API contract,
 * emulator base URL, and test calls. No secrets are stored or returned.
 */
declare class RozeMCPServer {
    private rl;
    constructor();
    private setupHandlers;
    private handleRequest;
    private callTool;
    private sendResponse;
    private setupErrorHandling;
    private log;
    start(): void;
}
export { RozeMCPServer };
//# sourceMappingURL=server.d.ts.map