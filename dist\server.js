#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.RozeMCPServer = void 0;
/**
 * Simple MCP Server implementation using JSON-RPC over stdio
 * Since we don't have the official MCP SDK, we'll implement the protocol directly
 */
const readline = __importStar(require("readline"));
const config_1 = require("./config");
const mcp_1 = require("./mcp");
/**
 * Roze MCP Bridge Server
 *
 * Exposes shared tools for VS Code windows to use the same API contract,
 * emulator base URL, and test calls. No secrets are stored or returned.
 */
class RozeMCPServer {
    rl;
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            terminal: false,
        });
        this.setupHandlers();
        this.setupErrorHandling();
    }
    setupHandlers() {
        this.rl.on('line', async (line) => {
            try {
                const request = JSON.parse(line);
                const response = await this.handleRequest(request);
                this.sendResponse(response);
            }
            catch (error) {
                this.log('error', `Failed to parse request: ${error}`);
            }
        });
    }
    async handleRequest(request) {
        try {
            switch (request.method) {
                case 'tools/list':
                    return {
                        jsonrpc: '2.0',
                        id: request.id,
                        result: {
                            tools: Object.entries(mcp_1.toolDefinitions).map(([name, definition]) => ({
                                name,
                                description: definition.description,
                                inputSchema: definition.inputSchema,
                            })),
                        },
                    };
                case 'tools/call':
                    const { name, arguments: args } = request.params;
                    const result = await this.callTool(name, args);
                    return {
                        jsonrpc: '2.0',
                        id: request.id,
                        result: {
                            content: [
                                {
                                    type: 'text',
                                    text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
                                },
                            ],
                        },
                    };
                case 'initialize':
                    return {
                        jsonrpc: '2.0',
                        id: request.id,
                        result: {
                            protocolVersion: '2024-11-05',
                            capabilities: {
                                tools: {},
                            },
                            serverInfo: {
                                name: 'roze-mcp',
                                version: '1.0.0',
                            },
                        },
                    };
                default:
                    return {
                        jsonrpc: '2.0',
                        id: request.id,
                        error: {
                            code: -32601,
                            message: `Method not found: ${request.method}`,
                        },
                    };
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.log('error', `Request failed: ${errorMessage}`);
            return {
                jsonrpc: '2.0',
                id: request.id,
                error: {
                    code: -32603,
                    message: 'Internal error',
                    data: errorMessage,
                },
            };
        }
    }
    async callTool(name, args) {
        switch (name) {
            case 'contracts.readOpenAPI':
                return await (0, mcp_1.readOpenAPI)();
            case 'contracts.readSchema':
                const schemaName = args?.name;
                if (!schemaName) {
                    throw new Error('Schema name is required');
                }
                return await (0, mcp_1.readSchema)(schemaName);
            case 'env.getApiBase':
                const target = args?.target;
                if (!target) {
                    throw new Error('Target environment is required');
                }
                return await (0, mcp_1.getApiBaseUrl)(target);
            case 'api.orders.create':
                const orderPayload = args?.payload;
                const orderTarget = args?.target;
                if (!orderPayload || !orderTarget) {
                    throw new Error('Payload and target are required');
                }
                return await (0, mcp_1.createOrder)(orderPayload, orderTarget);
            case 'api.subscribe.create':
                const subscribePayload = args?.payload;
                const subscribeTarget = args?.target;
                if (!subscribePayload || !subscribeTarget) {
                    throw new Error('Payload and target are required');
                }
                return await (0, mcp_1.createSubscription)(subscribePayload, subscribeTarget);
            case 'healthz':
                const healthTarget = args?.target;
                if (!healthTarget) {
                    throw new Error('Target environment is required');
                }
                return await (0, mcp_1.healthCheck)(healthTarget);
            default:
                throw new Error(`Unknown tool: ${name}`);
        }
    }
    sendResponse(response) {
        console.log(JSON.stringify(response));
    }
    setupErrorHandling() {
        process.on('SIGINT', () => {
            this.log('info', 'Received SIGINT, shutting down gracefully...');
            this.rl.close();
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            this.log('info', 'Received SIGTERM, shutting down gracefully...');
            this.rl.close();
            process.exit(0);
        });
        process.on('uncaughtException', (error) => {
            this.log('error', `Uncaught exception: ${error.message}`);
            process.exit(1);
        });
        process.on('unhandledRejection', (reason) => {
            this.log('error', `Unhandled rejection: ${reason}`);
            process.exit(1);
        });
    }
    log(level, message) {
        const timestamp = new Date().toISOString();
        const logLevel = config_1.config.logLevel;
        const levels = { debug: 0, info: 1, warn: 2, error: 3 };
        const currentLevel = levels[logLevel] || 1;
        const messageLevel = levels[level] || 1;
        if (messageLevel >= currentLevel) {
            console.error(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
        }
    }
    start() {
        this.log('info', 'MCP server ready');
        // Server is ready to receive JSON-RPC requests via stdin
    }
}
exports.RozeMCPServer = RozeMCPServer;
// Start the server
if (require.main === module) {
    const server = new RozeMCPServer();
    server.start();
}
//# sourceMappingURL=server.js.map