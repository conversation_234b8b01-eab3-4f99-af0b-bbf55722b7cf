"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.getApiBase = getApiBase;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
// Load environment variables
dotenv_1.default.config();
// Environment schema validation
const envSchema = zod_1.z.object({
    API_BASE_DEV: zod_1.z.string().url('API_BASE_DEV must be a valid URL'),
    API_BASE_PROD: zod_1.z.string().url('API_BASE_PROD must be a valid URL'),
    LOG_LEVEL: zod_1.z.enum(['debug', 'info', 'warn', 'error']).default('info'),
});
// Parse and validate environment
const env = envSchema.parse(process.env);
exports.config = {
    apiBaseDev: env.API_BASE_DEV,
    apiBaseProd: env.API_BASE_PROD,
    logLevel: env.LOG_LEVEL,
};
function getApiBase(target) {
    return target === 'dev' ? exports.config.apiBaseDev : exports.config.apiBaseProd;
}
//# sourceMappingURL=config.js.map