"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.toolDefinitions = void 0;
exports.readOpenAPI = readOpenAPI;
exports.readSchema = readSchema;
exports.getApiBaseUrl = getApiBaseUrl;
exports.createOrder = createOrder;
exports.createSubscription = createSubscription;
exports.healthCheck = healthCheck;
const ajv_1 = __importDefault(require("ajv"));
const ajv_formats_1 = __importDefault(require("ajv-formats"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const config_1 = require("./config");
const http_1 = require("./http");
// Initialize AJV with formats
const ajv = new ajv_1.default({ allErrors: true });
(0, ajv_formats_1.default)(ajv);
// Load and compile schemas
const orderCreateSchema = JSON.parse(fs.readFileSync(path.join(__dirname, '../schemas/order.create.json'), 'utf8'));
const subscribeCreateSchema = JSON.parse(fs.readFileSync(path.join(__dirname, '../schemas/subscribe.create.json'), 'utf8'));
const validateOrderCreate = ajv.compile(orderCreateSchema);
const validateSubscribeCreate = ajv.compile(subscribeCreateSchema);
// Schema name mapping
const schemaMap = {
    'order.create': orderCreateSchema,
    'subscribe.create': subscribeCreateSchema,
};
/**
 * MCP Tool: Read OpenAPI contract
 */
async function readOpenAPI() {
    try {
        const contractPath = path.join(__dirname, '../contracts/openapi.yaml');
        return fs.readFileSync(contractPath, 'utf8');
    }
    catch (error) {
        throw new Error(`Failed to read OpenAPI contract: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * MCP Tool: Read JSON Schema
 */
async function readSchema(name) {
    const schema = schemaMap[name];
    if (!schema) {
        throw new Error(`Schema '${name}' not found. Available schemas: ${Object.keys(schemaMap).join(', ')}`);
    }
    return schema;
}
/**
 * MCP Tool: Get API base URL
 */
async function getApiBaseUrl(target) {
    return { base: (0, config_1.getApiBase)(target) };
}
/**
 * MCP Tool: Create order
 */
async function createOrder(payload, target) {
    // Validate payload against schema
    const isValid = validateOrderCreate(payload);
    if (!isValid) {
        const errors = validateOrderCreate.errors?.map(err => `${err.instancePath || 'root'}: ${err.message}`).join('; ') || 'Validation failed';
        return {
            status: 400,
            body: { error: 'Validation failed', details: errors },
            ok: false,
            error: `Schema validation failed: ${errors}`,
        };
    }
    // Make API request
    const baseUrl = (0, config_1.getApiBase)(target);
    const url = `${baseUrl}/v1/orders`;
    return (0, http_1.makeRequest)('POST', url, payload);
}
/**
 * MCP Tool: Create subscription
 */
async function createSubscription(payload, target) {
    // Validate payload against schema
    const isValid = validateSubscribeCreate(payload);
    if (!isValid) {
        const errors = validateSubscribeCreate.errors?.map(err => `${err.instancePath || 'root'}: ${err.message}`).join('; ') || 'Validation failed';
        return {
            status: 400,
            body: { error: 'Validation failed', details: errors },
            ok: false,
            error: `Schema validation failed: ${errors}`,
        };
    }
    // Make API request
    const baseUrl = (0, config_1.getApiBase)(target);
    const url = `${baseUrl}/v1/subscribe`;
    return (0, http_1.makeRequest)('POST', url, payload);
}
/**
 * MCP Tool: Health check
 */
async function healthCheck(target) {
    const baseUrl = (0, config_1.getApiBase)(target);
    const url = `${baseUrl}/healthz`;
    const response = await (0, http_1.makeRequest)('GET', url);
    return {
        ok: response.ok,
        status: response.status,
        ...(response.error && { error: response.error }),
    };
}
/**
 * Tool definitions for MCP server registration
 */
exports.toolDefinitions = {
    'contracts_readOpenAPI': {
        description: 'Read the OpenAPI contract specification',
        inputSchema: {
            type: 'object',
            properties: {},
            required: [],
        },
    },
    'contracts_readSchema': {
        description: 'Read a JSON schema by name',
        inputSchema: {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    enum: ['order.create', 'subscribe.create'],
                    description: 'Schema name to retrieve',
                },
            },
            required: ['name'],
        },
    },
    'env_getApiBase': {
        description: 'Get API base URL for target environment',
        inputSchema: {
            type: 'object',
            properties: {
                target: {
                    type: 'string',
                    enum: ['dev', 'prod'],
                    description: 'Target environment',
                },
            },
            required: ['target'],
        },
    },
    'api_orders_create': {
        description: 'Create a new order with validation',
        inputSchema: {
            type: 'object',
            properties: {
                payload: {
                    type: 'object',
                    description: 'Order data to create',
                },
                target: {
                    type: 'string',
                    enum: ['dev', 'prod'],
                    description: 'Target environment',
                },
            },
            required: ['payload', 'target'],
        },
    },
    'api_subscribe_create': {
        description: 'Create a new subscription with validation',
        inputSchema: {
            type: 'object',
            properties: {
                payload: {
                    type: 'object',
                    description: 'Subscription data to create',
                },
                target: {
                    type: 'string',
                    enum: ['dev', 'prod'],
                    description: 'Target environment',
                },
            },
            required: ['payload', 'target'],
        },
    },
    'healthz': {
        description: 'Check API health status',
        inputSchema: {
            type: 'object',
            properties: {
                target: {
                    type: 'string',
                    enum: ['dev', 'prod'],
                    description: 'Target environment',
                },
            },
            required: ['target'],
        },
    },
};
//# sourceMappingURL=mcp.js.map