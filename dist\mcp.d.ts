import { type ApiTarget } from './config';
import { type ApiResponse } from './http';
declare const schemaMap: {
    readonly 'order.create': any;
    readonly 'subscribe.create': any;
};
export type SchemaName = keyof typeof schemaMap;
/**
 * MCP Tool: Read OpenAPI contract
 */
export declare function readOpenAPI(): Promise<string>;
/**
 * MCP Tool: Read JSON Schema
 */
export declare function readSchema(name: SchemaName): Promise<object>;
/**
 * MCP Tool: Get API base URL
 */
export declare function getApiBaseUrl(target: ApiTarget): Promise<{
    base: string;
}>;
/**
 * MCP Tool: Create order
 */
export declare function createOrder(payload: any, target: ApiTarget): Promise<ApiResponse>;
/**
 * MCP Tool: Create subscription
 */
export declare function createSubscription(payload: any, target: ApiTarget): Promise<ApiResponse>;
/**
 * MCP Tool: Health check
 */
export declare function healthCheck(target: ApiTarget): Promise<{
    ok: boolean;
    status?: number;
    error?: string;
}>;
/**
 * Tool definitions for MCP server registration
 */
export declare const toolDefinitions: {
    readonly contracts_readOpenAPI: {
        readonly description: "Read the OpenAPI contract specification";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {};
            readonly required: readonly [];
        };
    };
    readonly contracts_readSchema: {
        readonly description: "Read a JSON schema by name";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {
                readonly name: {
                    readonly type: "string";
                    readonly enum: readonly ["order.create", "subscribe.create"];
                    readonly description: "Schema name to retrieve";
                };
            };
            readonly required: readonly ["name"];
        };
    };
    readonly env_getApiBase: {
        readonly description: "Get API base URL for target environment";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {
                readonly target: {
                    readonly type: "string";
                    readonly enum: readonly ["dev", "prod"];
                    readonly description: "Target environment";
                };
            };
            readonly required: readonly ["target"];
        };
    };
    readonly api_orders_create: {
        readonly description: "Create a new order with validation";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {
                readonly payload: {
                    readonly type: "object";
                    readonly description: "Order data to create";
                };
                readonly target: {
                    readonly type: "string";
                    readonly enum: readonly ["dev", "prod"];
                    readonly description: "Target environment";
                };
            };
            readonly required: readonly ["payload", "target"];
        };
    };
    readonly api_subscribe_create: {
        readonly description: "Create a new subscription with validation";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {
                readonly payload: {
                    readonly type: "object";
                    readonly description: "Subscription data to create";
                };
                readonly target: {
                    readonly type: "string";
                    readonly enum: readonly ["dev", "prod"];
                    readonly description: "Target environment";
                };
            };
            readonly required: readonly ["payload", "target"];
        };
    };
    readonly healthz: {
        readonly description: "Check API health status";
        readonly inputSchema: {
            readonly type: "object";
            readonly properties: {
                readonly target: {
                    readonly type: "string";
                    readonly enum: readonly ["dev", "prod"];
                    readonly description: "Target environment";
                };
            };
            readonly required: readonly ["target"];
        };
    };
};
export {};
//# sourceMappingURL=mcp.d.ts.map